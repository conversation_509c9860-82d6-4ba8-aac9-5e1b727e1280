# Nacos命名空间配置说明

## 问题描述

项目中存在两个不同的Nacos命名空间配置，导致配置查找不一致的问题：

1. **registry.conf中配置的命名空间**: `4d307a39-1034-4861-83fd-c6921c6d9c02`
2. **NacosConstant.java中定义的命名空间**: `9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`

## 根本原因

### 配置优先级分析

1. **Spring Boot应用配置中心**：
   - 使用`NacosConstant.java`中定义的命名空间
   - 实际命名空间：`9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`
   - 影响：数据库连接配置、Redis配置、MongoDB配置等

2. **Seata分布式事务配置**：
   - 使用`registry.conf`中定义的命名空间
   - 配置命名空间：`4d307a39-1034-4861-83fd-c6921c6d9c02`
   - 影响：分布式事务相关配置

### 代码逻辑分析

```java
// NacosConstant.java 中的逻辑
public static DefaultUserName getDefaultUserName(Environment environment) {
    String uname = environment.getProperty("namespace", "default");
    DefaultUserName defaultUserName = null;
    switch (uname) {
        case NacosConstant.DEFAULT_USER_NAME :
            defaultUserName = new DefaultUserName();
            break;
        case NacosConstant.MES_USER_NAME :
            defaultUserName = new Mes();
            break;
        default:
            defaultUserName = new Mes(); // 默认使用Mes配置
    }
    return defaultUserName;
}
```

由于没有设置`namespace`属性，默认使用`Mes`类的配置，其命名空间为`9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`。

## 解决方案

### 方案一：统一使用9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05（推荐）

**优势**：
- 与现有配置保持一致
- 不需要修改大量现有配置
- MongoDB配置可以直接放在现有命名空间中

**操作步骤**：
1. 在Nacos中使用命名空间`9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`
2. 创建MongoDB配置：`mes-mongodb.properties`
3. 保持现有代码不变

### 方案二：修改NacosConstant.java使用4d307a39-1034-4861-83fd-c6921c6d9c02

**优势**：
- 与registry.conf配置保持一致
- 统一命名空间管理

**缺点**：
- 需要迁移现有所有配置到新命名空间
- 影响范围较大

## 当前MongoDB模块配置

### 实际使用的命名空间
- **Spring Boot配置中心**: `9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`
- **Seata事务配置**: `4d307a39-1034-4861-83fd-c6921c6d9c02`

### 配置位置
MongoDB连接配置应该放在：
- **命名空间**: `9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`
- **Data ID**: `mes-mongodb.properties`
- **Group**: `DEFAULT_GROUP`

## 验证方法

### 1. 查看应用启动日志
```bash
# 查看Nacos配置加载日志
grep -i "nacos" logs/mes-mongodb.log | grep -i "namespace"
```

### 2. 在Nacos控制台验证
1. 登录Nacos控制台
2. 切换到命名空间`9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`
3. 查看是否能找到`mes-mongodb.properties`配置

### 3. 代码调试
在`MongodbConfig.java`中添加日志输出，查看实际读取的配置值。

## 建议

1. **短期解决方案**：继续使用`9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`命名空间
2. **长期规划**：考虑统一所有配置到同一个命名空间
3. **文档更新**：更新所有相关文档，明确说明命名空间使用规则

## 注意事项

1. **配置迁移**：如果要统一命名空间，需要迁移所有现有配置
2. **环境一致性**：确保开发、测试、生产环境使用相同的命名空间策略
3. **团队沟通**：确保团队成员了解正确的命名空间配置
