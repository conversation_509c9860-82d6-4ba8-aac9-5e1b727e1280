package com.dinglite.mongodb.service.repository;

import com.dinglite.mongodb.api.domain.entity.ProductProcess;
import com.dinglite.mongodb.api.domain.query.ProductProcessQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 产品过站记录自定义Repository接口
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface ProductProcessCustomRepository {

    /**
     * 根据查询条件分页查询产品过站记录
     * 
     * @param query 查询条件
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<ProductProcess> findByQuery(ProductProcessQuery query, Pageable pageable);

    /**
     * 根据查询条件查询产品过站记录列表
     * 
     * @param query 查询条件
     * @return 记录列表
     */
    List<ProductProcess> findListByQuery(ProductProcessQuery query);

    /**
     * 根据查询条件统计记录数量
     * 
     * @param query 查询条件
     * @return 记录数量
     */
    long countByQuery(ProductProcessQuery query);

    /**
     * 根据产品序列号获取最新的过站记录
     * 
     * @param sn 产品序列号
     * @return 最新过站记录
     */
    ProductProcess findLatestBySn(String sn);

    /**
     * 根据产品序列号获取首道工序记录
     * 
     * @param sn 产品序列号
     * @return 首道工序记录
     */
    ProductProcess findFirstProcessBySn(String sn);

    /**
     * 获取指定产线的过站统计信息
     * 
     * @param lineCode 产线编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    List<ProcessStatistics> getProcessStatistics(String lineCode, String startTime, String endTime);

    /**
     * 过站统计信息内部类
     */
    class ProcessStatistics {
        private String processCode;
        private String processName;
        private long totalCount;
        private long passCount;
        private long failCount;
        private double passRate;

        // 构造函数、getter和setter方法
        public ProcessStatistics() {}

        public ProcessStatistics(String processCode, String processName, long totalCount, long passCount, long failCount) {
            this.processCode = processCode;
            this.processName = processName;
            this.totalCount = totalCount;
            this.passCount = passCount;
            this.failCount = failCount;
            this.passRate = totalCount > 0 ? (double) passCount / totalCount * 100 : 0.0;
        }

        public String getProcessCode() {
            return processCode;
        }

        public void setProcessCode(String processCode) {
            this.processCode = processCode;
        }

        public String getProcessName() {
            return processName;
        }

        public void setProcessName(String processName) {
            this.processName = processName;
        }

        public long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(long totalCount) {
            this.totalCount = totalCount;
        }

        public long getPassCount() {
            return passCount;
        }

        public void setPassCount(long passCount) {
            this.passCount = passCount;
        }

        public long getFailCount() {
            return failCount;
        }

        public void setFailCount(long failCount) {
            this.failCount = failCount;
        }

        public double getPassRate() {
            return passRate;
        }

        public void setPassRate(double passRate) {
            this.passRate = passRate;
        }
    }
}
