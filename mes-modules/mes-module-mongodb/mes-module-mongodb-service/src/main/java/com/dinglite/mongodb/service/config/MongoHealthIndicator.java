package com.dinglite.mongodb.service.config;

import com.mongodb.client.MongoClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * MongoDB健康检查指示器
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MongoHealthIndicator {

    private final MongoTemplate mongoTemplate;
    private final MongoClient mongoClient;

    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 执行简单的ping命令来检查连接
            mongoTemplate.getCollection("test").estimatedDocumentCount();

            // 获取数据库统计信息
            String databaseName = mongoTemplate.getDb().getName();

            result.put("status", "UP");
            result.put("database", databaseName);
            result.put("connection", "Connected");
            result.put("version", getMongoVersion());

        } catch (Exception e) {
            log.error("MongoDB健康检查失败", e);
            result.put("status", "DOWN");
            result.put("error", e.getMessage());
            result.put("connection", "Disconnected");
        }

        return result;
    }

    private String getMongoVersion() {
        try {
            return mongoTemplate.getDb()
                    .runCommand(new org.bson.Document("buildInfo", 1))
                    .getString("version");
        } catch (Exception e) {
            log.warn("无法获取MongoDB版本信息", e);
            return "Unknown";
        }
    }
}
