package com.dinglite.mongodb.service.repository;

import com.dinglite.mongodb.api.domain.entity.ProductProcess;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 产品过站记录Repository接口
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Repository
public interface ProductProcessRepository extends MongoRepository<ProductProcess, String> {

    /**
     * 根据产品序列号查询过站记录
     * 
     * @param sn 产品序列号
     * @return 过站记录列表
     */
    List<ProductProcess> findByMetaSn(String sn);

    /**
     * 根据产品序列号和工序编码查询过站记录
     * 
     * @param sn 产品序列号
     * @param processCode 工序编码
     * @return 过站记录
     */
    Optional<ProductProcess> findByMetaSnAndMetaProcessCode(String sn, String processCode);

    /**
     * 根据产线编码查询过站记录
     * 
     * @param lineCode 产线编码
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByMetaLineCode(String lineCode, Pageable pageable);

    /**
     * 根据测试结果查询过站记录
     * 
     * @param result 测试结果
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByResult(String result, Pageable pageable);

    /**
     * 根据时间范围查询过站记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByProcessTimeBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 根据产线编码和时间范围查询过站记录
     * 
     * @param lineCode 产线编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByMetaLineCodeAndProcessTimeBetween(String lineCode, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 根据工序编码查询过站记录
     * 
     * @param processCode 工序编码
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByMetaProcessCode(String processCode, Pageable pageable);

    /**
     * 根据操作员账号查询过站记录
     * 
     * @param operatorId 操作员账号
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByMetaOperatorId(String operatorId, Pageable pageable);

    /**
     * 根据工站名称查询过站记录
     * 
     * @param stationName 工站名称
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByStationName(String stationName, Pageable pageable);

    /**
     * 根据是否首道工序查询过站记录
     * 
     * @param isFirstProcess 是否首道工序
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByIsFirstProcess(Boolean isFirstProcess, Pageable pageable);

    /**
     * 根据是否补扫记录查询过站记录
     * 
     * @param isRepairScan 是否补扫记录
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByIsRepairScan(Boolean isRepairScan, Pageable pageable);

    /**
     * 根据产品序列号列表查询过站记录
     * 
     * @param snList 产品序列号列表
     * @return 过站记录列表
     */
    List<ProductProcess> findByMetaSnIn(List<String> snList);

    /**
     * 根据测试结果列表查询过站记录
     * 
     * @param resultList 测试结果列表
     * @param pageable 分页参数
     * @return 分页过站记录
     */
    Page<ProductProcess> findByResultIn(List<String> resultList, Pageable pageable);

    /**
     * 统计指定产线的过站记录数量
     * 
     * @param lineCode 产线编码
     * @return 记录数量
     */
    long countByMetaLineCode(String lineCode);

    /**
     * 统计指定时间范围内的过站记录数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录数量
     */
    long countByProcessTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定测试结果的记录数量
     * 
     * @param result 测试结果
     * @return 记录数量
     */
    long countByResult(String result);

    /**
     * 删除指定时间之前的过站记录
     * 
     * @param beforeTime 时间点
     * @return 删除的记录数量
     */
    long deleteByProcessTimeBefore(LocalDateTime beforeTime);
}
