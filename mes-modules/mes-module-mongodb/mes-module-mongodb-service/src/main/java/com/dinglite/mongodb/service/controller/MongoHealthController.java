package com.dinglite.mongodb.service.controller;

import com.dinglite.mongodb.api.constant.MongodbApiConstant;
import com.dinglite.mongodb.service.config.MongoHealthIndicator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * MongoDB健康检查控制器
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
@Api(tags = "MongoDB健康检查")
@RestController
@RequestMapping(MongodbApiConstant.API_BASE_PATH + "/health")
@RequiredArgsConstructor
public class MongoHealthController {

    private final MongoTemplate mongoTemplate;
    private final MongoHealthIndicator mongoHealthIndicator;

    @ApiOperation("检查MongoDB连接状态")
    @GetMapping("/check")
    public Map<String, Object> checkHealth() {
        Map<String, Object> result = mongoHealthIndicator.health();
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    @ApiOperation("获取MongoDB数据库信息")
    @GetMapping("/info")
    public Map<String, Object> getDatabaseInfo() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String databaseName = mongoTemplate.getDb().getName();
            result.put("databaseName", databaseName);
            result.put("status", "Connected");
            
            // 获取集合列表
            result.put("collections", mongoTemplate.getCollectionNames());
            
            // 获取数据库统计信息
            org.bson.Document stats = mongoTemplate.getDb().runCommand(new org.bson.Document("dbStats", 1));
            result.put("dbStats", stats);
            
        } catch (Exception e) {
            result.put("status", "Error");
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @ApiOperation("测试MongoDB连接")
    @GetMapping("/ping")
    public Map<String, Object> ping() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 执行ping命令
            org.bson.Document pingResult = mongoTemplate.getDb().runCommand(new org.bson.Document("ping", 1));
            result.put("ping", "success");
            result.put("response", pingResult);
            result.put("timestamp", System.currentTimeMillis());
        } catch (Exception e) {
            result.put("ping", "failed");
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        
        return result;
    }

    @ApiOperation("获取MongoDB版本信息")
    @GetMapping("/version")
    public Map<String, Object> getVersion() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            org.bson.Document buildInfo = mongoTemplate.getDb().runCommand(new org.bson.Document("buildInfo", 1));
            result.put("version", buildInfo.getString("version"));
            result.put("gitVersion", buildInfo.getString("gitVersion"));
            result.put("buildInfo", buildInfo);
        } catch (Exception e) {
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
