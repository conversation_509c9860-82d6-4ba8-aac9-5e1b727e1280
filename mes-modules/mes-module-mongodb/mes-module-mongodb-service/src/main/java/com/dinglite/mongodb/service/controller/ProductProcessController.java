package com.dinglite.mongodb.service.controller;

import com.dinglite.common.annotation.LoadPayload;
import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.api.constant.MongodbApiConstant;
import com.dinglite.mongodb.api.domain.dto.ProductProcessDTO;
import com.dinglite.mongodb.api.domain.query.ProductProcessQuery;
import com.dinglite.mongodb.service.repository.ProductProcessCustomRepository;
import com.dinglite.mongodb.service.service.ProductProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品过站记录控制器
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Api(tags = "产品过站记录管理")
@RestController
@RequestMapping(MongodbApiConstant.PRODUCT_PROCESS_API_PATH)
@RequiredArgsConstructor
public class ProductProcessController {

    private final ProductProcessService productProcessService;

    @ApiOperation("创建产品过站记录")
    @PostMapping
    @LoadPayload
    public ProductProcessDTO create(@RequestBody ProductProcessDTO productProcessDTO) {
        return productProcessService.create(productProcessDTO);
    }

    @ApiOperation("批量创建产品过站记录")
    @PostMapping("/batch")
    @LoadPayload
    public Integer batchCreate(@RequestBody List<ProductProcessDTO> productProcessDTOList) {
        return productProcessService.batchCreate(productProcessDTOList);
    }

    @ApiOperation("根据ID获取产品过站记录")
    @GetMapping("/{id}")
    public ProductProcessDTO getById(@ApiParam("记录ID") @PathVariable String id) {
        return productProcessService.getById(id);
    }

    @ApiOperation("根据产品序列号获取过站记录列表")
    @GetMapping("/sn/{sn}")
    public List<ProductProcessDTO> getBySn(@ApiParam("产品序列号") @PathVariable String sn) {
        return productProcessService.getBySn(sn);
    }

    @ApiOperation("根据产品序列号获取最新的过站记录")
    @GetMapping("/latest/{sn}")
    public ProductProcessDTO getLatestBySn(@ApiParam("产品序列号") @PathVariable String sn) {
        return productProcessService.getLatestBySn(sn);
    }

    @ApiOperation("根据产品序列号获取首道工序记录")
    @GetMapping("/first/{sn}")
    public ProductProcessDTO getFirstProcessBySn(@ApiParam("产品序列号") @PathVariable String sn) {
        return productProcessService.getFirstProcessBySn(sn);
    }

    @ApiOperation("根据产品序列号和工序编码获取过站记录")
    @GetMapping("/sn/{sn}/process/{processCode}")
    public ProductProcessDTO getBySnAndProcessCode(
            @ApiParam("产品序列号") @PathVariable String sn,
            @ApiParam("工序编码") @PathVariable String processCode) {
        return productProcessService.getBySnAndProcessCode(sn, processCode);
    }

    @ApiOperation("更新产品过站记录")
    @PutMapping
    @LoadPayload
    public Boolean update(@RequestBody ProductProcessDTO productProcessDTO) {
        return productProcessService.update(productProcessDTO);
    }

    @ApiOperation("根据ID删除产品过站记录")
    @DeleteMapping("/{id}")
    public Boolean deleteById(@ApiParam("记录ID") @PathVariable String id) {
        return productProcessService.deleteById(id);
    }

    @ApiOperation("批量删除产品过站记录")
    @DeleteMapping("/batch")
    public Boolean batchDelete(@RequestBody List<String> ids) {
        return productProcessService.batchDelete(ids);
    }

    @ApiOperation("分页查询产品过站记录")
    @PostMapping("/page")
    @LoadPayload
    public PageDTO<ProductProcessDTO> page(@RequestBody ProductProcessQuery query) {
        return productProcessService.page(query);
    }

    @ApiOperation("根据查询条件获取记录列表")
    @PostMapping("/list")
    @LoadPayload
    public List<ProductProcessDTO> list(@RequestBody ProductProcessQuery query) {
        return productProcessService.list(query);
    }

    @ApiOperation("根据查询条件统计记录数量")
    @PostMapping("/count")
    @LoadPayload
    public Long count(@RequestBody ProductProcessQuery query) {
        return productProcessService.count(query);
    }

    @ApiOperation("获取指定产线的过站统计信息")
    @GetMapping("/statistics/{lineCode}")
    public List<ProductProcessCustomRepository.ProcessStatistics> getProcessStatistics(
            @ApiParam("产线编码") @PathVariable String lineCode,
            @ApiParam("开始时间") @RequestParam String startTime,
            @ApiParam("结束时间") @RequestParam String endTime) {
        return productProcessService.getProcessStatistics(lineCode, 
                LocalDateTime.parse(startTime), LocalDateTime.parse(endTime));
    }

    @ApiOperation("统计指定产线的过站记录数量")
    @GetMapping("/count/line/{lineCode}")
    public Long countByLineCode(@ApiParam("产线编码") @PathVariable String lineCode) {
        return productProcessService.countByLineCode(lineCode);
    }

    @ApiOperation("统计指定时间范围内的过站记录数量")
    @GetMapping("/count/time")
    public Long countByTimeRange(
            @ApiParam("开始时间") @RequestParam String startTime,
            @ApiParam("结束时间") @RequestParam String endTime) {
        return productProcessService.countByTimeRange(
                LocalDateTime.parse(startTime), LocalDateTime.parse(endTime));
    }

    @ApiOperation("统计指定测试结果的记录数量")
    @GetMapping("/count/result/{result}")
    public Long countByResult(@ApiParam("测试结果") @PathVariable String result) {
        return productProcessService.countByResult(result);
    }

    @ApiOperation("删除指定时间之前的过站记录")
    @DeleteMapping("/time/{beforeTime}")
    public Long deleteByTimeBefore(@ApiParam("时间点") @PathVariable String beforeTime) {
        return productProcessService.deleteByTimeBefore(LocalDateTime.parse(beforeTime));
    }

    @ApiOperation("检查产品是否已过指定工序")
    @GetMapping("/check/{sn}/process/{processCode}")
    public Boolean hasPassedProcess(
            @ApiParam("产品序列号") @PathVariable String sn,
            @ApiParam("工序编码") @PathVariable String processCode) {
        return productProcessService.hasPassedProcess(sn, processCode);
    }

    @ApiOperation("获取产品的过站路径")
    @GetMapping("/path/{sn}")
    public List<String> getProcessPath(@ApiParam("产品序列号") @PathVariable String sn) {
        return productProcessService.getProcessPath(sn);
    }
}
