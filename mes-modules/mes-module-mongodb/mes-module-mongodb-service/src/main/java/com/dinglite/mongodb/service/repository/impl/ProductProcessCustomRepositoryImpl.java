package com.dinglite.mongodb.service.repository.impl;

import com.dinglite.mongodb.api.domain.entity.ProductProcess;
import com.dinglite.mongodb.api.domain.query.ProductProcessQuery;
import com.dinglite.mongodb.service.repository.ProductProcessCustomRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品过站记录自定义Repository实现类
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Repository
@RequiredArgsConstructor
public class ProductProcessCustomRepositoryImpl implements ProductProcessCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Page<ProductProcess> findByQuery(ProductProcessQuery query, Pageable pageable) {
        Query mongoQuery = buildQuery(query);
        
        // 设置分页
        mongoQuery.with(pageable);
        
        // 执行查询
        List<ProductProcess> list = mongoTemplate.find(mongoQuery, ProductProcess.class);
        
        // 使用相同的查询条件进行计数，但不包含分页信息
        Query countQuery = buildQuery(query);
        long total = mongoTemplate.count(countQuery, ProductProcess.class);
        
        return new PageImpl<>(list, pageable, total);
    }

    @Override
    public List<ProductProcess> findListByQuery(ProductProcessQuery query) {
        Query mongoQuery = buildQuery(query);
        return mongoTemplate.find(mongoQuery, ProductProcess.class);
    }

    @Override
    public long countByQuery(ProductProcessQuery query) {
        Query mongoQuery = buildQuery(query);
        return mongoTemplate.count(mongoQuery, ProductProcess.class);
    }

    @Override
    public ProductProcess findLatestBySn(String sn) {
        Query query = new Query();
        query.addCriteria(Criteria.where("meta.sn").is(sn));
        query.with(Sort.by(Sort.Direction.DESC, "processTime"));
        query.limit(1);
        
        List<ProductProcess> results = mongoTemplate.find(query, ProductProcess.class);
        return results.isEmpty() ? null : results.get(0);
    }

    @Override
    public ProductProcess findFirstProcessBySn(String sn) {
        Query query = new Query();
        query.addCriteria(Criteria.where("meta.sn").is(sn));
        query.addCriteria(Criteria.where("isFirstProcess").is(true));
        query.with(Sort.by(Sort.Direction.ASC, "processTime"));
        query.limit(1);
        
        List<ProductProcess> results = mongoTemplate.find(query, ProductProcess.class);
        return results.isEmpty() ? null : results.get(0);
    }

    @Override
    public List<ProcessStatistics> getProcessStatistics(String lineCode, String startTime, String endTime) {
        // 简化实现，使用基础查询和Java计算
        Query query = new Query();
        query.addCriteria(Criteria.where("meta.lineCode").is(lineCode)
            .and("processTime").gte(startTime).lte(endTime));

        List<ProductProcess> processes = mongoTemplate.find(query, ProductProcess.class);

        // 使用Java Stream进行分组统计
        Map<String, List<ProductProcess>> groupedByProcess = processes.stream()
            .collect(Collectors.groupingBy(p -> p.getMeta().getProcessCode()));

        List<ProcessStatistics> statistics = new ArrayList<>();
        for (Map.Entry<String, List<ProductProcess>> entry : groupedByProcess.entrySet()) {
            String processCode = entry.getKey();
            List<ProductProcess> processList = entry.getValue();

            long totalCount = processList.size();
            long passCount = processList.stream().filter(p -> "PASS".equals(p.getResult())).count();
            long failCount = processList.stream().filter(p -> "FAIL".equals(p.getResult())).count();

            String processName = processList.isEmpty() ? processCode : processList.get(0).getProcessName();

            statistics.add(new ProcessStatistics(processCode, processName, totalCount, passCount, failCount));
        }

        return statistics;
    }

    /**
     * 构建查询条件
     * 
     * @param query 查询参数
     * @return MongoDB查询对象
     */
    private Query buildQuery(ProductProcessQuery query) {
        Query mongoQuery = new Query();
        List<Criteria> criteriaList = new ArrayList<>();

        // 产品序列号
        if (StringUtils.hasText(query.getSn())) {
            criteriaList.add(Criteria.where("meta.sn").is(query.getSn()));
        }

        // 产品序列号列表
        if (!CollectionUtils.isEmpty(query.getSnList())) {
            criteriaList.add(Criteria.where("meta.sn").in(query.getSnList()));
        }

        // 产线编码
        if (StringUtils.hasText(query.getLineCode())) {
            criteriaList.add(Criteria.where("meta.lineCode").is(query.getLineCode()));
        }

        // 产线名称
        if (StringUtils.hasText(query.getLineName())) {
            criteriaList.add(Criteria.where("lineName").regex(query.getLineName(), "i"));
        }

        // 工序编码
        if (StringUtils.hasText(query.getProcessCode())) {
            criteriaList.add(Criteria.where("meta.processCode").is(query.getProcessCode()));
        }

        // 工序名称
        if (StringUtils.hasText(query.getProcessName())) {
            criteriaList.add(Criteria.where("processName").regex(query.getProcessName(), "i"));
        }

        // 工站名称
        if (StringUtils.hasText(query.getStationName())) {
            criteriaList.add(Criteria.where("stationName").regex(query.getStationName(), "i"));
        }

        // 工站IP
        if (StringUtils.hasText(query.getStationIP())) {
            criteriaList.add(Criteria.where("stationIP").is(query.getStationIP()));
        }

        // 操作员账号
        if (StringUtils.hasText(query.getOperatorId())) {
            criteriaList.add(Criteria.where("meta.operatorId").is(query.getOperatorId()));
        }

        // 操作员姓名
        if (StringUtils.hasText(query.getOperatorName())) {
            criteriaList.add(Criteria.where("operatorName").regex(query.getOperatorName(), "i"));
        }

        // 测试结果
        if (StringUtils.hasText(query.getResult())) {
            criteriaList.add(Criteria.where("result").is(query.getResult()));
        }

        // 测试结果列表
        if (!CollectionUtils.isEmpty(query.getResultList())) {
            criteriaList.add(Criteria.where("result").in(query.getResultList()));
        }

        // 是否首道工序
        if (query.getIsFirstProcess() != null) {
            criteriaList.add(Criteria.where("isFirstProcess").is(query.getIsFirstProcess()));
        }

        // 是否补扫记录
        if (query.getIsRepairScan() != null) {
            criteriaList.add(Criteria.where("isRepairScan").is(query.getIsRepairScan()));
        }

        // 过站时间范围
        if (query.getProcessTimeStart() != null && query.getProcessTimeEnd() != null) {
            criteriaList.add(Criteria.where("processTime").gte(query.getProcessTimeStart()).lte(query.getProcessTimeEnd()));
        } else if (query.getProcessTimeStart() != null) {
            criteriaList.add(Criteria.where("processTime").gte(query.getProcessTimeStart()));
        } else if (query.getProcessTimeEnd() != null) {
            criteriaList.add(Criteria.where("processTime").lte(query.getProcessTimeEnd()));
        }

        // 扫描时间范围
        if (query.getScanTimeStart() != null && query.getScanTimeEnd() != null) {
            criteriaList.add(Criteria.where("scanTime").gte(query.getScanTimeStart()).lte(query.getScanTimeEnd()));
        }

        // 提交时间范围
        if (query.getSubmitTimeStart() != null && query.getSubmitTimeEnd() != null) {
            criteriaList.add(Criteria.where("submitTime").gte(query.getSubmitTimeStart()).lte(query.getSubmitTimeEnd()));
        }

        // 治具相关
        if (StringUtils.hasText(query.getFixtureName())) {
            criteriaList.add(Criteria.where("fixtureName").regex(query.getFixtureName(), "i"));
        }
        if (StringUtils.hasText(query.getFixtureCode())) {
            criteriaList.add(Criteria.where("fixtureCode").is(query.getFixtureCode()));
        }
        if (StringUtils.hasText(query.getFixtureVersion())) {
            criteriaList.add(Criteria.where("fixtureVersion").is(query.getFixtureVersion()));
        }

        // 扫描编码
        if (StringUtils.hasText(query.getScanCode())) {
            criteriaList.add(Criteria.where("scanCode").is(query.getScanCode()));
        }

        // 不良相关
        if (StringUtils.hasText(query.getFailName())) {
            criteriaList.add(Criteria.where("failName").regex(query.getFailName(), "i"));
        }
        if (StringUtils.hasText(query.getFailCode())) {
            criteriaList.add(Criteria.where("failCode").is(query.getFailCode()));
        }

        // 重投工序相关
        if (StringUtils.hasText(query.getReworkProcessCode())) {
            criteriaList.add(Criteria.where("reworkProcessCode").is(query.getReworkProcessCode()));
        }
        if (StringUtils.hasText(query.getReworkProcessName())) {
            criteriaList.add(Criteria.where("reworkProcessName").regex(query.getReworkProcessName(), "i"));
        }

        // 组合所有条件
        if (!criteriaList.isEmpty()) {
            mongoQuery.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }

        // 设置排序
        if (StringUtils.hasText(query.getOrderField())) {
            Sort.Direction direction = "asc".equalsIgnoreCase(query.getOrderType()) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
            mongoQuery.with(Sort.by(direction, query.getOrderField()));
        } else {
            // 默认按过站时间倒序
            mongoQuery.with(Sort.by(Sort.Direction.DESC, "processTime"));
        }

        return mongoQuery;
    }
}
