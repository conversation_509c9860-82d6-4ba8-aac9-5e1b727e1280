package com.dinglite.mongodb.service.service;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.api.domain.dto.ProductInfoDTO;
import com.dinglite.mongodb.api.domain.entity.ProductInfo;
import com.dinglite.mongodb.api.domain.query.ProductInfoQuery;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品基础信息服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface ProductInfoService {

    /**
     * 创建产品基础信息
     * 
     * @param productInfoDTO 产品信息DTO
     * @return 创建的记录DTO
     */
    ProductInfoDTO create(ProductInfoDTO productInfoDTO);

    /**
     * 批量创建产品基础信息
     * 
     * @param productInfoDTOList 产品信息DTO列表
     * @return 创建成功的数量
     */
    Integer batchCreate(List<ProductInfoDTO> productInfoDTOList);

    /**
     * 根据ID获取产品基础信息
     * 
     * @param id 记录ID
     * @return 产品信息DTO
     */
    ProductInfoDTO getById(String id);

    /**
     * 根据产品序列号获取产品信息
     * 
     * @param sn 产品序列号
     * @return 产品信息DTO
     */
    ProductInfoDTO getBySn(String sn);

    /**
     * 根据产品序列号列表获取产品信息
     * 
     * @param snList 产品序列号列表
     * @return 产品信息列表
     */
    List<ProductInfoDTO> getBySnList(List<String> snList);

    /**
     * 更新产品基础信息
     * 
     * @param productInfoDTO 产品信息DTO
     * @return 是否更新成功
     */
    Boolean update(ProductInfoDTO productInfoDTO);

    /**
     * 根据ID删除产品基础信息
     * 
     * @param id 记录ID
     * @return 是否删除成功
     */
    Boolean deleteById(String id);

    /**
     * 批量删除产品基础信息
     * 
     * @param ids 记录ID列表
     * @return 是否删除成功
     */
    Boolean batchDelete(List<String> ids);

    /**
     * 分页查询产品基础信息
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageDTO<ProductInfoDTO> page(ProductInfoQuery query);

    /**
     * 根据查询条件获取记录列表
     * 
     * @param query 查询条件
     * @return 记录列表
     */
    List<ProductInfoDTO> list(ProductInfoQuery query);

    /**
     * 根据查询条件统计记录数量
     * 
     * @param query 查询条件
     * @return 记录数量
     */
    Long count(ProductInfoQuery query);

    /**
     * 统计指定产线的产品数量
     * 
     * @param lineCode 产线编码
     * @return 产品数量
     */
    Long countByLineCode(String lineCode);

    /**
     * 统计指定状态的产品数量
     * 
     * @param status 产品状态
     * @return 产品数量
     */
    Long countByStatus(String status);

    /**
     * 统计指定时间范围内投产的产品数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 产品数量
     */
    Long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 检查产品序列号是否存在
     * 
     * @param sn 产品序列号
     * @return 是否存在
     */
    Boolean existsBySn(String sn);

    /**
     * 根据包装编码获取产品信息
     * 
     * @param packageCode 包装编码
     * @return 产品信息列表
     */
    List<ProductInfoDTO> getByPackageCode(String packageCode);

    /**
     * 根据内箱编码获取产品信息
     * 
     * @param innerBoxCode 内箱编码
     * @return 产品信息列表
     */
    List<ProductInfoDTO> getByInnerBoxCode(String innerBoxCode);

    /**
     * 根据外箱编码获取产品信息
     * 
     * @param outerBoxCode 外箱编码
     * @return 产品信息列表
     */
    List<ProductInfoDTO> getByOuterBoxCode(String outerBoxCode);

    /**
     * 根据栈板编码获取产品信息
     * 
     * @param palletCode 栈板编码
     * @return 产品信息列表
     */
    List<ProductInfoDTO> getByPalletCode(String palletCode);

    /**
     * 更新产品状态
     * 
     * @param sn 产品序列号
     * @param status 新状态
     * @return 是否更新成功
     */
    Boolean updateStatus(String sn, String status);

    /**
     * 更新产品包装状态
     * 
     * @param sn 产品序列号
     * @param packageStatus 包装状态
     * @return 是否更新成功
     */
    Boolean updatePackageStatus(String sn, String packageStatus);

    /**
     * 更新产品最后工序信息
     * 
     * @param sn 产品序列号
     * @param lastProcess 最后工序
     * @param lastStation 最后工站
     * @return 是否更新成功
     */
    Boolean updateLastProcess(String sn, String lastProcess, String lastStation);

    /**
     * 添加已完成工序
     * 
     * @param sn 产品序列号
     * @param processCode 工序编码
     * @return 是否更新成功
     */
    Boolean addCompletedProcess(String sn, String processCode);

    /**
     * 实体转DTO
     * 
     * @param productInfo 实体对象
     * @return DTO对象
     */
    ProductInfoDTO toDTO(ProductInfo productInfo);

    /**
     * DTO转实体
     * 
     * @param productInfoDTO DTO对象
     * @return 实体对象
     */
    ProductInfo toEntity(ProductInfoDTO productInfoDTO);

    /**
     * 实体列表转DTO列表
     * 
     * @param productInfoList 实体列表
     * @return DTO列表
     */
    List<ProductInfoDTO> toDTOList(List<ProductInfo> productInfoList);
}
