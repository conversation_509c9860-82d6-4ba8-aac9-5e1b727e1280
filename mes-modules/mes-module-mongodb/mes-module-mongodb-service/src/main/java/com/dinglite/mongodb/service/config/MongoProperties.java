package com.dinglite.mongodb.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MongoDB连接属性配置
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@Component
@ConfigurationProperties(prefix = "spring.data.mongodb.option")
public class MongoProperties {

    /**
     * 每个主机的连接数
     */
    private Integer connectionsPerHost = 100;

    /**
     * 每个主机的最小连接数
     */
    private Integer minConnectionsPerHost = 10;

    /**
     * 连接超时时间(毫秒)
     */
    private Integer connectTimeout = 10000;

    /**
     * 最大等待时间(毫秒)
     */
    private Integer maxWaitTime = 120000;

    /**
     * Socket超时时间(毫秒)
     */
    private Integer socketTimeout = 0;

    /**
     * 保持Socket连接
     */
    private Boolean socketKeepAlive = false;

    /**
     * 最大连接空闲时间(毫秒)
     */
    private Integer maxConnectionIdleTime = 0;

    /**
     * 最大连接生命周期(毫秒)
     */
    private Integer maxConnectionLifeTime = 0;

    /**
     * 心跳频率(毫秒)
     */
    private Integer heartbeatFrequency = 10000;

    /**
     * 最小心跳频率(毫秒)
     */
    private Integer minHeartbeatFrequency = 500;

    /**
     * 心跳连接超时时间(毫秒)
     */
    private Integer heartbeatConnectTimeout = 20000;

    /**
     * 心跳Socket超时时间(毫秒)
     */
    private Integer heartbeatSocketTimeout = 20000;

    /**
     * 本地阈值(毫秒)
     */
    private Integer localThreshold = 15;

    /**
     * 是否启用SSL
     */
    private Boolean sslEnabled = false;

    /**
     * 是否允许无效的主机名
     */
    private Boolean sslInvalidHostnameAllowed = false;

    /**
     * 是否始终使用MBeans
     */
    private Boolean alwaysUseMBeans = false;
}
