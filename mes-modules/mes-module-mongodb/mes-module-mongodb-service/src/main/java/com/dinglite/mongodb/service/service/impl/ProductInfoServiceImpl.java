package com.dinglite.mongodb.service.service.impl;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.service.util.PageUtils;
import com.dinglite.mongodb.api.domain.dto.ProductInfoDTO;
import com.dinglite.mongodb.api.domain.entity.ProductInfo;
import com.dinglite.mongodb.api.domain.query.ProductInfoQuery;
import com.dinglite.mongodb.service.repository.ProductInfoRepository;
import com.dinglite.mongodb.service.service.ProductInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产品基础信息服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductInfoServiceImpl implements ProductInfoService {

    private final ProductInfoRepository productInfoRepository;
    private final MongoTemplate mongoTemplate;

    @Override
    public ProductInfoDTO create(ProductInfoDTO productInfoDTO) {
        try {
            ProductInfo productInfo = toEntity(productInfoDTO);
            ProductInfo saved = productInfoRepository.save(productInfo);
            log.info("创建产品基础信息成功，ID: {}, SN: {}", saved.getId(), saved.getSn());
            return toDTO(saved);
        } catch (Exception e) {
            log.error("创建产品基础信息失败", e);
            throw new RuntimeException("创建产品基础信息失败: " + e.getMessage());
        }
    }

    @Override
    public Integer batchCreate(List<ProductInfoDTO> productInfoDTOList) {
        if (CollectionUtils.isEmpty(productInfoDTOList)) {
            return 0;
        }
        
        try {
            List<ProductInfo> productInfoList = productInfoDTOList.stream()
                    .map(this::toEntity)
                    .collect(Collectors.toList());
            
            List<ProductInfo> savedList = productInfoRepository.saveAll(productInfoList);
            log.info("批量创建产品基础信息成功，数量: {}", savedList.size());
            return savedList.size();
        } catch (Exception e) {
            log.error("批量创建产品基础信息失败", e);
            throw new RuntimeException("批量创建产品基础信息失败: " + e.getMessage());
        }
    }

    @Override
    public ProductInfoDTO getById(String id) {
        Optional<ProductInfo> optional = productInfoRepository.findById(id);
        return optional.map(this::toDTO).orElse(null);
    }

    @Override
    public ProductInfoDTO getBySn(String sn) {
        Optional<ProductInfo> optional = productInfoRepository.findBySn(sn);
        return optional.map(this::toDTO).orElse(null);
    }

    @Override
    public List<ProductInfoDTO> getBySnList(List<String> snList) {
        List<ProductInfo> list = productInfoRepository.findBySnIn(snList);
        return toDTOList(list);
    }

    @Override
    public Boolean update(ProductInfoDTO productInfoDTO) {
        try {
            ProductInfo productInfo = toEntity(productInfoDTO);
            productInfoRepository.save(productInfo);
            log.info("更新产品基础信息成功，ID: {}", productInfo.getId());
            return true;
        } catch (Exception e) {
            log.error("更新产品基础信息失败，ID: {}", productInfoDTO.getId(), e);
            return false;
        }
    }

    @Override
    public Boolean deleteById(String id) {
        try {
            productInfoRepository.deleteById(id);
            log.info("删除产品基础信息成功，ID: {}", id);
            return true;
        } catch (Exception e) {
            log.error("删除产品基础信息失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    public Boolean batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        
        try {
            for (String id : ids) {
                productInfoRepository.deleteById(id);
            }
            log.info("批量删除产品基础信息成功，数量: {}", ids.size());
            return true;
        } catch (Exception e) {
            log.error("批量删除产品基础信息失败", e);
            return false;
        }
    }

    @Override
    public PageDTO<ProductInfoDTO> page(ProductInfoQuery query) {
        Pageable pageable = PageRequest.of(query.getCurrent() - 1, query.getSize());
        
        // 构建查询条件
        Query mongoQuery = buildQuery(query);
        mongoQuery.with(pageable);
        
        // 执行查询
        List<ProductInfo> list = mongoTemplate.find(mongoQuery, ProductInfo.class);

        // 使用相同的查询条件进行计数，但不包含分页信息
        Query countQuery = buildQuery(query);
        long total = mongoTemplate.count(countQuery, ProductInfo.class);
        
        List<ProductInfoDTO> dtoList = toDTOList(list);
        return PageUtils.toDTO(total, dtoList, query.getCurrent(), query.getSize());
    }

    @Override
    public List<ProductInfoDTO> list(ProductInfoQuery query) {
        Query mongoQuery = buildQuery(query);
        List<ProductInfo> list = mongoTemplate.find(mongoQuery, ProductInfo.class);
        return toDTOList(list);
    }

    @Override
    public Long count(ProductInfoQuery query) {
        Query mongoQuery = buildQuery(query);
        return mongoTemplate.count(mongoQuery, ProductInfo.class);
    }

    @Override
    public Long countByLineCode(String lineCode) {
        return productInfoRepository.countByLineCode(lineCode);
    }

    @Override
    public Long countByStatus(String status) {
        return productInfoRepository.countByStatus(status);
    }

    @Override
    public Long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return productInfoRepository.countByStartTimeBetween(startTime, endTime);
    }

    @Override
    public Boolean existsBySn(String sn) {
        return productInfoRepository.existsBySn(sn);
    }

    @Override
    public List<ProductInfoDTO> getByPackageCode(String packageCode) {
        List<ProductInfo> list = productInfoRepository.findByPackageCode(packageCode);
        return toDTOList(list);
    }

    @Override
    public List<ProductInfoDTO> getByInnerBoxCode(String innerBoxCode) {
        List<ProductInfo> list = productInfoRepository.findByInnerBoxCode(innerBoxCode);
        return toDTOList(list);
    }

    @Override
    public List<ProductInfoDTO> getByOuterBoxCode(String outerBoxCode) {
        List<ProductInfo> list = productInfoRepository.findByOuterBoxCode(outerBoxCode);
        return toDTOList(list);
    }

    @Override
    public List<ProductInfoDTO> getByPalletCode(String palletCode) {
        List<ProductInfo> list = productInfoRepository.findByPalletCode(palletCode);
        return toDTOList(list);
    }

    @Override
    public Boolean updateStatus(String sn, String status) {
        try {
            Query query = new Query(Criteria.where("sn").is(sn));
            Update update = new Update().set("status", status);
            mongoTemplate.updateFirst(query, update, ProductInfo.class);
            log.info("更新产品状态成功，SN: {}, Status: {}", sn, status);
            return true;
        } catch (Exception e) {
            log.error("更新产品状态失败，SN: {}, Status: {}", sn, status, e);
            return false;
        }
    }

    @Override
    public Boolean updatePackageStatus(String sn, String packageStatus) {
        try {
            Query query = new Query(Criteria.where("sn").is(sn));
            Update update = new Update().set("packageStatus", packageStatus);
            mongoTemplate.updateFirst(query, update, ProductInfo.class);
            log.info("更新产品包装状态成功，SN: {}, PackageStatus: {}", sn, packageStatus);
            return true;
        } catch (Exception e) {
            log.error("更新产品包装状态失败，SN: {}, PackageStatus: {}", sn, packageStatus, e);
            return false;
        }
    }

    @Override
    public Boolean updateLastProcess(String sn, String lastProcess, String lastStation) {
        try {
            Query query = new Query(Criteria.where("sn").is(sn));
            Update update = new Update()
                    .set("lastProcess", lastProcess)
                    .set("lastStation", lastStation);
            mongoTemplate.updateFirst(query, update, ProductInfo.class);
            log.info("更新产品最后工序信息成功，SN: {}", sn);
            return true;
        } catch (Exception e) {
            log.error("更新产品最后工序信息失败，SN: {}", sn, e);
            return false;
        }
    }

    @Override
    public Boolean addCompletedProcess(String sn, String processCode) {
        try {
            Query query = new Query(Criteria.where("sn").is(sn));
            Update update = new Update().addToSet("completedProcesses", processCode);
            mongoTemplate.updateFirst(query, update, ProductInfo.class);
            log.info("添加已完成工序成功，SN: {}, ProcessCode: {}", sn, processCode);
            return true;
        } catch (Exception e) {
            log.error("添加已完成工序失败，SN: {}, ProcessCode: {}", sn, processCode, e);
            return false;
        }
    }

    @Override
    public ProductInfoDTO toDTO(ProductInfo productInfo) {
        if (productInfo == null) {
            return null;
        }
        
        ProductInfoDTO dto = new ProductInfoDTO();
        BeanUtils.copyProperties(productInfo, dto);
        return dto;
    }

    @Override
    public ProductInfo toEntity(ProductInfoDTO productInfoDTO) {
        if (productInfoDTO == null) {
            return null;
        }
        
        ProductInfo entity = new ProductInfo();
        BeanUtils.copyProperties(productInfoDTO, entity);
        return entity;
    }

    @Override
    public List<ProductInfoDTO> toDTOList(List<ProductInfo> productInfoList) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return new ArrayList<>();
        }
        
        return productInfoList.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private Query buildQuery(ProductInfoQuery query) {
        Query mongoQuery = new Query();
        List<Criteria> criteriaList = new ArrayList<>();

        // 产品序列号
        if (StringUtils.hasText(query.getSn())) {
            criteriaList.add(Criteria.where("sn").is(query.getSn()));
        }

        // 产品序列号列表
        if (!CollectionUtils.isEmpty(query.getSnList())) {
            criteriaList.add(Criteria.where("sn").in(query.getSnList()));
        }

        // 产品型号相关
        if (StringUtils.hasText(query.getModelName())) {
            criteriaList.add(Criteria.where("modelName").regex(query.getModelName(), "i"));
        }
        if (StringUtils.hasText(query.getModelCode())) {
            criteriaList.add(Criteria.where("modelCode").is(query.getModelCode()));
        }

        // 客户相关
        if (StringUtils.hasText(query.getCustomerPN())) {
            criteriaList.add(Criteria.where("customerPN").is(query.getCustomerPN()));
        }
        if (StringUtils.hasText(query.getCustomerOrder())) {
            criteriaList.add(Criteria.where("customerOrder").is(query.getCustomerOrder()));
        }

        // 生产相关
        if (StringUtils.hasText(query.getProductionOrder())) {
            criteriaList.add(Criteria.where("productionOrder").is(query.getProductionOrder()));
        }
        if (StringUtils.hasText(query.getWorkshopName())) {
            criteriaList.add(Criteria.where("workshopName").regex(query.getWorkshopName(), "i"));
        }

        // 产线相关
        if (StringUtils.hasText(query.getLineName())) {
            criteriaList.add(Criteria.where("lineName").regex(query.getLineName(), "i"));
        }
        if (StringUtils.hasText(query.getLineCode())) {
            criteriaList.add(Criteria.where("lineCode").is(query.getLineCode()));
        }

        // 状态相关
        if (StringUtils.hasText(query.getStatus())) {
            criteriaList.add(Criteria.where("status").is(query.getStatus()));
        }
        if (!CollectionUtils.isEmpty(query.getStatusList())) {
            criteriaList.add(Criteria.where("status").in(query.getStatusList()));
        }

        // 是否直通品
        if (query.getIsDirectProduct() != null) {
            criteriaList.add(Criteria.where("isDirectProduct").is(query.getIsDirectProduct()));
        }

        // 投产时间范围
        if (query.getStartTimeStart() != null && query.getStartTimeEnd() != null) {
            criteriaList.add(Criteria.where("startTime").gte(query.getStartTimeStart()).lte(query.getStartTimeEnd()));
        }

        // 包装相关
        if (StringUtils.hasText(query.getPackageStatus())) {
            criteriaList.add(Criteria.where("packageStatus").is(query.getPackageStatus()));
        }
        if (!CollectionUtils.isEmpty(query.getPackageStatusList())) {
            criteriaList.add(Criteria.where("packageStatus").in(query.getPackageStatusList()));
        }

        // 组合所有条件
        if (!criteriaList.isEmpty()) {
            mongoQuery.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[0])));
        }

        // 设置排序
        if (StringUtils.hasText(query.getOrderField())) {
            Sort.Direction direction = "asc".equalsIgnoreCase(query.getOrderType()) ? 
                Sort.Direction.ASC : Sort.Direction.DESC;
            mongoQuery.with(Sort.by(direction, query.getOrderField()));
        } else {
            // 默认按投产时间倒序
            mongoQuery.with(Sort.by(Sort.Direction.DESC, "startTime"));
        }

        return mongoQuery;
    }
}
