package com.dinglite.mongodb.service.service.impl;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.service.util.PageUtils;
import com.dinglite.mongodb.api.domain.dto.ProductProcessDTO;
import com.dinglite.mongodb.api.domain.entity.ProductProcess;
import com.dinglite.mongodb.api.domain.query.ProductProcessQuery;
import com.dinglite.mongodb.service.repository.ProductProcessCustomRepository;
import com.dinglite.mongodb.service.repository.ProductProcessRepository;
import com.dinglite.mongodb.service.service.ProductProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产品过站记录服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductProcessServiceImpl implements ProductProcessService {

    private final ProductProcessRepository productProcessRepository;
    private final ProductProcessCustomRepository productProcessCustomRepository;

    @Override
    public ProductProcessDTO create(ProductProcessDTO productProcessDTO) {
        try {
            ProductProcess productProcess = toEntity(productProcessDTO);
            ProductProcess saved = productProcessRepository.save(productProcess);
            log.info("创建产品过站记录成功，ID: {}, SN: {}", saved.getId(), saved.getMeta().getSn());
            return toDTO(saved);
        } catch (Exception e) {
            log.error("创建产品过站记录失败", e);
            throw new RuntimeException("创建产品过站记录失败: " + e.getMessage());
        }
    }

    @Override
    public Integer batchCreate(List<ProductProcessDTO> productProcessDTOList) {
        if (CollectionUtils.isEmpty(productProcessDTOList)) {
            return 0;
        }
        
        try {
            List<ProductProcess> productProcessList = productProcessDTOList.stream()
                    .map(this::toEntity)
                    .collect(Collectors.toList());
            
            List<ProductProcess> savedList = productProcessRepository.saveAll(productProcessList);
            log.info("批量创建产品过站记录成功，数量: {}", savedList.size());
            return savedList.size();
        } catch (Exception e) {
            log.error("批量创建产品过站记录失败", e);
            throw new RuntimeException("批量创建产品过站记录失败: " + e.getMessage());
        }
    }

    @Override
    public ProductProcessDTO getById(String id) {
        Optional<ProductProcess> optional = productProcessRepository.findById(id);
        return optional.map(this::toDTO).orElse(null);
    }

    @Override
    public List<ProductProcessDTO> getBySn(String sn) {
        List<ProductProcess> list = productProcessRepository.findByMetaSn(sn);
        return toDTOList(list);
    }

    @Override
    public ProductProcessDTO getLatestBySn(String sn) {
        ProductProcess productProcess = productProcessCustomRepository.findLatestBySn(sn);
        return toDTO(productProcess);
    }

    @Override
    public ProductProcessDTO getFirstProcessBySn(String sn) {
        ProductProcess productProcess = productProcessCustomRepository.findFirstProcessBySn(sn);
        return toDTO(productProcess);
    }

    @Override
    public ProductProcessDTO getBySnAndProcessCode(String sn, String processCode) {
        Optional<ProductProcess> optional = productProcessRepository.findByMetaSnAndMetaProcessCode(sn, processCode);
        return optional.map(this::toDTO).orElse(null);
    }

    @Override
    public Boolean update(ProductProcessDTO productProcessDTO) {
        try {
            ProductProcess productProcess = toEntity(productProcessDTO);
            productProcessRepository.save(productProcess);
            log.info("更新产品过站记录成功，ID: {}", productProcess.getId());
            return true;
        } catch (Exception e) {
            log.error("更新产品过站记录失败，ID: {}", productProcessDTO.getId(), e);
            return false;
        }
    }

    @Override
    public Boolean deleteById(String id) {
        try {
            productProcessRepository.deleteById(id);
            log.info("删除产品过站记录成功，ID: {}", id);
            return true;
        } catch (Exception e) {
            log.error("删除产品过站记录失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    public Boolean batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        
        try {
            for (String id : ids) {
                productProcessRepository.deleteById(id);
            }
            log.info("批量删除产品过站记录成功，数量: {}", ids.size());
            return true;
        } catch (Exception e) {
            log.error("批量删除产品过站记录失败", e);
            return false;
        }
    }

    @Override
    public PageDTO<ProductProcessDTO> page(ProductProcessQuery query) {
        Pageable pageable = PageRequest.of(query.getCurrent() - 1, query.getSize());
        Page<ProductProcess> page = productProcessCustomRepository.findByQuery(query, pageable);
        
        List<ProductProcessDTO> dtoList = toDTOList(page.getContent());
        return PageUtils.toDTO(page.getTotalElements(), dtoList, query.getCurrent(), query.getSize());
    }

    @Override
    public List<ProductProcessDTO> list(ProductProcessQuery query) {
        List<ProductProcess> list = productProcessCustomRepository.findListByQuery(query);
        return toDTOList(list);
    }

    @Override
    public Long count(ProductProcessQuery query) {
        return productProcessCustomRepository.countByQuery(query);
    }

    @Override
    public List<ProductProcessCustomRepository.ProcessStatistics> getProcessStatistics(String lineCode, LocalDateTime startTime, LocalDateTime endTime) {
        return productProcessCustomRepository.getProcessStatistics(lineCode, startTime.toString(), endTime.toString());
    }

    @Override
    public Long countByLineCode(String lineCode) {
        return productProcessRepository.countByMetaLineCode(lineCode);
    }

    @Override
    public Long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return productProcessRepository.countByProcessTimeBetween(startTime, endTime);
    }

    @Override
    public Long countByResult(String result) {
        return productProcessRepository.countByResult(result);
    }

    @Override
    public Long deleteByTimeBefore(LocalDateTime beforeTime) {
        return productProcessRepository.deleteByProcessTimeBefore(beforeTime);
    }

    @Override
    public Boolean hasPassedProcess(String sn, String processCode) {
        Optional<ProductProcess> optional = productProcessRepository.findByMetaSnAndMetaProcessCode(sn, processCode);
        return optional.isPresent();
    }

    @Override
    public List<String> getProcessPath(String sn) {
        List<ProductProcess> list = productProcessRepository.findByMetaSn(sn);
        return list.stream()
                .sorted((p1, p2) -> p1.getProcessTime().compareTo(p2.getProcessTime()))
                .map(p -> p.getMeta().getProcessCode())
                .collect(Collectors.toList());
    }

    @Override
    public ProductProcessDTO toDTO(ProductProcess productProcess) {
        if (productProcess == null) {
            return null;
        }
        
        ProductProcessDTO dto = new ProductProcessDTO();
        BeanUtils.copyProperties(productProcess, dto);
        
        // 处理嵌套对象
        if (productProcess.getMeta() != null) {
            ProductProcessDTO.MetaDataDTO metaDTO = new ProductProcessDTO.MetaDataDTO();
            BeanUtils.copyProperties(productProcess.getMeta(), metaDTO);
            dto.setMeta(metaDTO);
        }
        
        return dto;
    }

    @Override
    public ProductProcess toEntity(ProductProcessDTO productProcessDTO) {
        if (productProcessDTO == null) {
            return null;
        }
        
        ProductProcess entity = new ProductProcess();
        BeanUtils.copyProperties(productProcessDTO, entity);
        
        // 处理嵌套对象
        if (productProcessDTO.getMeta() != null) {
            ProductProcess.MetaData meta = new ProductProcess.MetaData();
            BeanUtils.copyProperties(productProcessDTO.getMeta(), meta);
            entity.setMeta(meta);
        }
        
        return entity;
    }

    @Override
    public List<ProductProcessDTO> toDTOList(List<ProductProcess> productProcessList) {
        if (CollectionUtils.isEmpty(productProcessList)) {
            return new ArrayList<>();
        }
        
        return productProcessList.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
}
