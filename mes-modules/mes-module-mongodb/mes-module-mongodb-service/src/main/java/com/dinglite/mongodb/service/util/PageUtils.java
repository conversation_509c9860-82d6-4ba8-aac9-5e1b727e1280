package com.dinglite.mongodb.service.util;

import com.dinglite.common.domain.PageDTO;

import java.util.List;

/**
 * MongoDB分页工具类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
public class PageUtils {

    /**
     * 创建分页DTO
     * 
     * @param total 总记录数
     * @param records 当前页记录
     * @param current 当前页码
     * @param size 每页大小
     * @param <T> 记录类型
     * @return 分页DTO
     */
    public static <T> PageDTO<T> toDTO(long total, List<T> records, int current, int size) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setTotal(total);
        pageDTO.setRecords(records);
        pageDTO.setCurrent(current);
        pageDTO.setSize(size);
        return pageDTO;
    }

    /**
     * 创建分页DTO
     * 
     * @param total 总记录数
     * @param records 当前页记录
     * @param current 当前页码
     * @param size 每页大小
     * @param <T> 记录类型
     * @return 分页DTO
     */
    public static <T> PageDTO<T> toDTO(long total, List<T> records, long current, long size) {
        return toDTO(total, records, (int) current, (int) size);
    }
}
