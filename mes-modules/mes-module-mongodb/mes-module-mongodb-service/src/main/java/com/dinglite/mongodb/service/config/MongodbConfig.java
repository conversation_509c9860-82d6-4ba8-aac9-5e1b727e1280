package com.dinglite.mongodb.service.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;

import java.util.concurrent.TimeUnit;

/**
 * MongoDB配置类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Configuration
public class MongodbConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.database:mes_mongodb}")
    private String database;

    @Value("${spring.data.mongodb.host:127.0.0.1}")
    private String host;

    @Value("${spring.data.mongodb.port:27017}")
    private int port;

    @Value("${spring.data.mongodb.username:admin}")
    private String username;

    @Value("${spring.data.mongodb.password:123456}")
    private String password;

    @Value("${spring.data.mongodb.authentication-database:admin}")
    private String authDatabase;

    // 连接池配置参数
    @Value("${spring.data.mongodb.option.connections-per-host:100}")
    private int maxSize;

    @Value("${spring.data.mongodb.option.min-connections-per-host:10}")
    private int minSize;

    @Value("${spring.data.mongodb.option.max-wait-time:120000}")
    private int maxWaitTime;

    @Value("${spring.data.mongodb.option.max-connection-idle-time:0}")
    private int maxConnectionIdleTime;

    @Value("${spring.data.mongodb.option.max-connection-life-time:0}")
    private int maxConnectionLifeTime;

    @Value("${spring.data.mongodb.option.connect-timeout:10000}")
    private int connectTimeout;

    @Value("${spring.data.mongodb.option.socket-timeout:0}")
    private int socketTimeout;

    @Value("${spring.data.mongodb.option.heartbeat-frequency:10000}")
    private int heartbeatFrequency;

    @Value("${spring.data.mongodb.option.min-heartbeat-frequency:500}")
    private int minHeartbeatFrequency;

    @Override
    protected String getDatabaseName() {
        return database;
    }

    @Override
    @Bean
    public MongoClient mongoClient() {
        // 构建连接字符串
        String connectionString;
        if (username != null && !username.trim().isEmpty() &&
            password != null && !password.trim().isEmpty()) {
            connectionString = String.format("mongodb://%s:%s@%s:%d/%s?authSource=%s",
                username, password, host, port, database, authDatabase);
        } else {
            connectionString = String.format("mongodb://%s:%d/%s", host, port, database);
        }

        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(connectionString))
                .applyToConnectionPoolSettings(builder ->
                    builder.maxSize(maxSize)
                           .minSize(minSize)
                           .maxWaitTime(maxWaitTime, TimeUnit.MILLISECONDS)
                           .maxConnectionIdleTime(maxConnectionIdleTime, TimeUnit.MILLISECONDS)
                           .maxConnectionLifeTime(maxConnectionLifeTime, TimeUnit.MILLISECONDS))
                .applyToSocketSettings(builder ->
                    builder.connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                           .readTimeout(socketTimeout, TimeUnit.MILLISECONDS))
                .applyToServerSettings(builder ->
                    builder.heartbeatFrequency(heartbeatFrequency, TimeUnit.MILLISECONDS)
                           .minHeartbeatFrequency(minHeartbeatFrequency, TimeUnit.MILLISECONDS))
                .build();

        return MongoClients.create(settings);
    }

    @Bean
    public MongoTemplate mongoTemplate() throws Exception {
        MongoTemplate mongoTemplate = new MongoTemplate(mongoClient(), getDatabaseName());

        // 移除_class字段
        MappingMongoConverter converter = (MappingMongoConverter) mongoTemplate.getConverter();
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));

        return mongoTemplate;
    }

    @Bean
    public MongoCustomConversions customConversions() {
        return new MongoCustomConversions(java.util.Collections.emptyList());
    }
}
