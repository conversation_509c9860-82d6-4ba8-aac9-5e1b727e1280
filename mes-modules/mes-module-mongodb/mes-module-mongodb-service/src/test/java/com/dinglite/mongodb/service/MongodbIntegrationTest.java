package com.dinglite.mongodb.service;

import com.dinglite.mongodb.api.domain.entity.ProductInfo;
import com.dinglite.mongodb.api.domain.entity.ProductProcess;
import com.dinglite.mongodb.service.repository.ProductInfoRepository;
import com.dinglite.mongodb.service.repository.ProductProcessRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MongoDB集成测试类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@SpringBootTest
@ActiveProfiles("test")
class MongodbIntegrationTest {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ProductProcessRepository productProcessRepository;

    @Autowired
    private ProductInfoRepository productInfoRepository;

    @Test
    void testMongoConnection() {
        // 测试MongoDB连接
        assertNotNull(mongoTemplate);
        
        // 测试数据库操作
        String databaseName = mongoTemplate.getDb().getName();
        assertNotNull(databaseName);
        assertTrue(databaseName.contains("test"));
    }

    @Test
    void testProductProcessRepository() {
        // 创建测试数据
        ProductProcess productProcess = createTestProductProcess();
        
        // 保存数据
        ProductProcess saved = productProcessRepository.save(productProcess);
        assertNotNull(saved.getId());
        
        // 查询数据
        Optional<ProductProcess> found = productProcessRepository.findById(saved.getId());
        assertTrue(found.isPresent());
        assertEquals(productProcess.getMeta().getSn(), found.get().getMeta().getSn());
        
        // 根据SN查询
        List<ProductProcess> bySnList = productProcessRepository.findByMetaSn(productProcess.getMeta().getSn());
        assertFalse(bySnList.isEmpty());
        
        // 统计数量
        long count = productProcessRepository.countByMetaLineCode(productProcess.getMeta().getLineCode());
        assertTrue(count > 0);
        
        // 删除测试数据
        productProcessRepository.deleteById(saved.getId());
    }

    @Test
    void testProductInfoRepository() {
        // 创建测试数据
        ProductInfo productInfo = createTestProductInfo();
        
        // 保存数据
        ProductInfo saved = productInfoRepository.save(productInfo);
        assertNotNull(saved.getId());
        
        // 查询数据
        Optional<ProductInfo> found = productInfoRepository.findById(saved.getId());
        assertTrue(found.isPresent());
        assertEquals(productInfo.getSn(), found.get().getSn());
        
        // 根据SN查询
        Optional<ProductInfo> bySn = productInfoRepository.findBySn(productInfo.getSn());
        assertTrue(bySn.isPresent());
        
        // 检查存在性
        boolean exists = productInfoRepository.existsBySn(productInfo.getSn());
        assertTrue(exists);
        
        // 统计数量
        long count = productInfoRepository.countByLineCode(productInfo.getLineCode());
        assertTrue(count > 0);
        
        // 删除测试数据
        productInfoRepository.deleteById(saved.getId());
    }

    @Test
    void testMongoTemplateOperations() {
        // 测试MongoTemplate直接操作
        ProductProcess productProcess = createTestProductProcess();
        
        // 保存
        ProductProcess saved = mongoTemplate.save(productProcess);
        assertNotNull(saved.getId());
        
        // 查询
        ProductProcess found = mongoTemplate.findById(saved.getId(), ProductProcess.class);
        assertNotNull(found);
        assertEquals(productProcess.getMeta().getSn(), found.getMeta().getSn());
        
        // 删除
        mongoTemplate.remove(found);
        
        // 验证删除
        ProductProcess deleted = mongoTemplate.findById(saved.getId(), ProductProcess.class);
        assertNull(deleted);
    }

    @Test
    void testCollectionOperations() {
        // 测试集合操作
        String collectionName = "product_process";
        
        // 检查集合是否存在
        boolean exists = mongoTemplate.collectionExists(collectionName);
        // 如果不存在，创建集合
        if (!exists) {
            mongoTemplate.createCollection(collectionName);
        }
        
        // 获取集合名称列表
        Set<String> collections = mongoTemplate.getCollectionNames();
        assertNotNull(collections);
        assertTrue(collections.contains(collectionName));
    }

    /**
     * 创建测试用的ProductProcess
     */
    private ProductProcess createTestProductProcess() {
        ProductProcess productProcess = new ProductProcess();
        productProcess.setCollection("product_process");
        productProcess.setProcessTime(LocalDateTime.now());
        productProcess.setResult("PASS");
        productProcess.setScanTime(LocalDateTime.now());
        productProcess.setSubmitTime(LocalDateTime.now());
        productProcess.setLineName("测试产线");
        productProcess.setStationName("测试工站");
        productProcess.setStationIP("*************");
        productProcess.setOperatorName("测试操作员");
        productProcess.setProcessName("测试工序");
        productProcess.setIsFirstProcess(false);
        productProcess.setIsRepairScan(false);
        
        // 设置元数据
        ProductProcess.MetaData meta = new ProductProcess.MetaData();
        meta.setSn("TEST_SN_" + System.currentTimeMillis());
        meta.setLineCode("LINE001");
        meta.setOperatorId("OP001");
        meta.setProcessCode("PROC001");
        productProcess.setMeta(meta);
        
        return productProcess;
    }

    /**
     * 创建测试用的ProductInfo
     */
    private ProductInfo createTestProductInfo() {
        ProductInfo productInfo = new ProductInfo();
        productInfo.setCollection("product_info");
        productInfo.setSn("TEST_PRODUCT_" + System.currentTimeMillis());
        productInfo.setModelName("测试型号");
        productInfo.setModelCode("MODEL001");
        productInfo.setCustomerPN("CUSTOMER_PN_001");
        productInfo.setCustomerOrder("ORDER_001");
        productInfo.setProductionOrder("PROD_ORDER_001");
        productInfo.setWorkshopName("测试车间");
        productInfo.setLineName("测试产线");
        productInfo.setLineCode("LINE001");
        productInfo.setStatus("直通品");
        productInfo.setIsDirectProduct(true);
        productInfo.setStartTime(LocalDateTime.now());
        productInfo.setLastProcess("测试工序");
        productInfo.setLastStation("测试工站");
        productInfo.setPackageStatus("未包装");
        
        return productInfo;
    }
}
