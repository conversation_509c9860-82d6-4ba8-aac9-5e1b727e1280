package com.dinglite.mongodb.service.service;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.api.domain.dto.ProductProcessDTO;
import com.dinglite.mongodb.api.domain.query.ProductProcessQuery;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 产品过站记录服务测试类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@SpringBootTest
@ActiveProfiles("test")
class ProductProcessServiceTest {

    @Autowired
    private ProductProcessService productProcessService;

    @Test
    void testCreateProductProcess() {
        // 准备测试数据
        ProductProcessDTO dto = createTestProductProcessDTO();
        
        // 执行测试
        ProductProcessDTO result = productProcessService.create(dto);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(dto.getMeta().getSn(), result.getMeta().getSn());
        assertEquals(dto.getResult(), result.getResult());
    }

    @Test
    void testBatchCreateProductProcess() {
        // 准备测试数据
        List<ProductProcessDTO> dtoList = Arrays.asList(
            createTestProductProcessDTO("TEST001", "PASS"),
            createTestProductProcessDTO("TEST002", "FAIL")
        );
        
        // 执行测试
        Integer result = productProcessService.batchCreate(dtoList);
        
        // 验证结果
        assertEquals(2, result.intValue());
    }

    @Test
    void testGetBySn() {
        // 先创建测试数据
        ProductProcessDTO dto = createTestProductProcessDTO("TEST003", "PASS");
        productProcessService.create(dto);
        
        // 执行测试
        List<ProductProcessDTO> result = productProcessService.getBySn("TEST003");
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("TEST003", result.get(0).getMeta().getSn());
    }

    @Test
    void testGetLatestBySn() {
        // 先创建测试数据
        String sn = "TEST004";
        ProductProcessDTO dto1 = createTestProductProcessDTO(sn, "PASS");
        dto1.setProcessTime(LocalDateTime.now().minusHours(1));
        productProcessService.create(dto1);
        
        ProductProcessDTO dto2 = createTestProductProcessDTO(sn, "FAIL");
        dto2.setProcessTime(LocalDateTime.now());
        productProcessService.create(dto2);
        
        // 执行测试
        ProductProcessDTO result = productProcessService.getLatestBySn(sn);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("FAIL", result.getResult());
    }

    @Test
    void testPageQuery() {
        // 准备查询条件
        ProductProcessQuery query = new ProductProcessQuery();
        query.setCurrent(1);
        query.setSize(10);
        query.setResult("PASS");
        
        // 执行测试
        PageDTO<ProductProcessDTO> result = productProcessService.page(query);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getTotal() >= 0);
        assertNotNull(result.getRecords());
    }

    @Test
    void testCountByLineCode() {
        // 先创建测试数据
        ProductProcessDTO dto = createTestProductProcessDTO();
        dto.getMeta().setLineCode("LINE001");
        productProcessService.create(dto);
        
        // 执行测试
        Long result = productProcessService.countByLineCode("LINE001");
        
        // 验证结果
        assertTrue(result >= 1);
    }

    @Test
    void testHasPassedProcess() {
        // 先创建测试数据
        String sn = "TEST005";
        String processCode = "PROC001";
        ProductProcessDTO dto = createTestProductProcessDTO(sn, "PASS");
        dto.getMeta().setProcessCode(processCode);
        productProcessService.create(dto);
        
        // 执行测试
        Boolean result = productProcessService.hasPassedProcess(sn, processCode);
        
        // 验证结果
        assertTrue(result);
        
        // 测试不存在的工序
        Boolean result2 = productProcessService.hasPassedProcess(sn, "PROC999");
        assertFalse(result2);
    }

    @Test
    void testGetProcessPath() {
        // 先创建测试数据
        String sn = "TEST006";
        
        ProductProcessDTO dto1 = createTestProductProcessDTO(sn, "PASS");
        dto1.getMeta().setProcessCode("PROC001");
        dto1.setProcessTime(LocalDateTime.now().minusHours(2));
        productProcessService.create(dto1);
        
        ProductProcessDTO dto2 = createTestProductProcessDTO(sn, "PASS");
        dto2.getMeta().setProcessCode("PROC002");
        dto2.setProcessTime(LocalDateTime.now().minusHours(1));
        productProcessService.create(dto2);
        
        // 执行测试
        List<String> result = productProcessService.getProcessPath(sn);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("PROC001", result.get(0));
        assertEquals("PROC002", result.get(1));
    }

    /**
     * 创建测试用的ProductProcessDTO
     */
    private ProductProcessDTO createTestProductProcessDTO() {
        return createTestProductProcessDTO("TEST_SN_" + System.currentTimeMillis(), "PASS");
    }

    /**
     * 创建测试用的ProductProcessDTO
     */
    private ProductProcessDTO createTestProductProcessDTO(String sn, String result) {
        ProductProcessDTO dto = new ProductProcessDTO();
        dto.setCollection("product_process");
        dto.setProcessTime(LocalDateTime.now());
        dto.setResult(result);
        dto.setScanTime(LocalDateTime.now());
        dto.setSubmitTime(LocalDateTime.now());
        dto.setLineName("测试产线");
        dto.setStationName("测试工站");
        dto.setStationIP("*************");
        dto.setOperatorName("测试操作员");
        dto.setProcessName("测试工序");
        dto.setIsFirstProcess(false);
        dto.setIsRepairScan(false);
        
        // 设置元数据
        ProductProcessDTO.MetaDataDTO meta = new ProductProcessDTO.MetaDataDTO();
        meta.setSn(sn);
        meta.setLineCode("LINE001");
        meta.setOperatorId("OP001");
        meta.setProcessCode("PROC001");
        dto.setMeta(meta);
        
        // 设置参数
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("voltage", 12.5);
        parameters.put("temperature", 25.0);
        dto.setParameters(parameters);
        
        return dto;
    }
}
