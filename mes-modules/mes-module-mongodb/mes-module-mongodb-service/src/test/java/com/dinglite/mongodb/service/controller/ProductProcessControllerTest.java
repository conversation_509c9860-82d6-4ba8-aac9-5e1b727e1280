package com.dinglite.mongodb.service.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.dinglite.mongodb.api.domain.dto.ProductProcessDTO;
import com.dinglite.mongodb.api.domain.query.ProductProcessQuery;
import com.dinglite.mongodb.service.service.ProductProcessService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ProductProcessController测试类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@WebMvcTest(ProductProcessController.class)
class ProductProcessControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProductProcessService productProcessService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testCreateProductProcess() throws Exception {
        // 准备测试数据
        ProductProcessDTO dto = createTestProductProcessDTO();
        ProductProcessDTO result = createTestProductProcessDTO();
        result.setId("test_id_123");

        // Mock服务层
        when(productProcessService.create(any(ProductProcessDTO.class))).thenReturn(result);

        // 执行测试
        mockMvc.perform(post("/mongodb/product-process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("test_id_123"))
                .andExpect(jsonPath("$.meta.sn").value(dto.getMeta().getSn()));
    }

    @Test
    void testGetById() throws Exception {
        // 准备测试数据
        String id = "test_id_123";
        ProductProcessDTO result = createTestProductProcessDTO();
        result.setId(id);

        // Mock服务层
        when(productProcessService.getById(anyString())).thenReturn(result);

        // 执行测试
        mockMvc.perform(get("/mongodb/product-process/{id}", id))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(id))
                .andExpect(jsonPath("$.meta.sn").value(result.getMeta().getSn()));
    }

    @Test
    void testGetBySn() throws Exception {
        // 准备测试数据
        String sn = "TEST_SN_001";
        ProductProcessDTO dto = createTestProductProcessDTO();
        dto.getMeta().setSn(sn);

        // Mock服务层
        when(productProcessService.getBySn(anyString())).thenReturn(Arrays.asList(dto));

        // 执行测试
        mockMvc.perform(get("/mongodb/product-process/sn/{sn}", sn))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].meta.sn").value(sn));
    }

    @Test
    void testGetLatestBySn() throws Exception {
        // 准备测试数据
        String sn = "TEST_SN_001";
        ProductProcessDTO dto = createTestProductProcessDTO();
        dto.getMeta().setSn(sn);

        // Mock服务层
        when(productProcessService.getLatestBySn(anyString())).thenReturn(dto);

        // 执行测试
        mockMvc.perform(get("/mongodb/product-process/latest/{sn}", sn))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.meta.sn").value(sn));
    }

    @Test
    void testUpdate() throws Exception {
        // 准备测试数据
        ProductProcessDTO dto = createTestProductProcessDTO();
        dto.setId("test_id_123");

        // Mock服务层
        when(productProcessService.update(any(ProductProcessDTO.class))).thenReturn(true);

        // 执行测试
        mockMvc.perform(put("/mongodb/product-process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }

    @Test
    void testDeleteById() throws Exception {
        // 准备测试数据
        String id = "test_id_123";

        // Mock服务层
        when(productProcessService.deleteById(anyString())).thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/mongodb/product-process/{id}", id))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }

    @Test
    void testPageQuery() throws Exception {
        // 准备测试数据
        ProductProcessQuery query = new ProductProcessQuery();
        query.setCurrent(1);
        query.setSize(10);
        query.setResult("PASS");

        // Mock服务层
        when(productProcessService.page(any(ProductProcessQuery.class))).thenReturn(new com.dinglite.common.domain.PageDTO<>());

        // 执行测试
        mockMvc.perform(post("/mongodb/product-process/page")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(query)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.total").exists())
                .andExpect(jsonPath("$.records").exists());
    }

    @Test
    void testCountByLineCode() throws Exception {
        // 准备测试数据
        String lineCode = "LINE001";

        // Mock服务层
        when(productProcessService.countByLineCode(anyString())).thenReturn(100L);

        // 执行测试
        mockMvc.perform(get("/mongodb/product-process/count/line/{lineCode}", lineCode))
                .andExpect(status().isOk())
                .andExpect(content().string("100"));
    }

    @Test
    void testHasPassedProcess() throws Exception {
        // 准备测试数据
        String sn = "TEST_SN_001";
        String processCode = "PROC001";

        // Mock服务层
        when(productProcessService.hasPassedProcess(anyString(), anyString())).thenReturn(true);

        // 执行测试
        mockMvc.perform(get("/mongodb/product-process/check/{sn}/process/{processCode}", sn, processCode))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }

    @Test
    void testGetProcessPath() throws Exception {
        // 准备测试数据
        String sn = "TEST_SN_001";

        // Mock服务层
        when(productProcessService.getProcessPath(anyString())).thenReturn(Arrays.asList("PROC001", "PROC002"));

        // 执行测试
        mockMvc.perform(get("/mongodb/product-process/path/{sn}", sn))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0]").value("PROC001"))
                .andExpect(jsonPath("$[1]").value("PROC002"));
    }

    /**
     * 创建测试用的ProductProcessDTO
     */
    private ProductProcessDTO createTestProductProcessDTO() {
        ProductProcessDTO dto = new ProductProcessDTO();
        dto.setCollection("product_process");
        dto.setProcessTime(LocalDateTime.now());
        dto.setResult("PASS");
        dto.setScanTime(LocalDateTime.now());
        dto.setSubmitTime(LocalDateTime.now());
        dto.setLineName("测试产线");
        dto.setStationName("测试工站");
        dto.setStationIP("*************");
        dto.setOperatorName("测试操作员");
        dto.setProcessName("测试工序");
        dto.setIsFirstProcess(false);
        dto.setIsRepairScan(false);
        
        // 设置元数据
        ProductProcessDTO.MetaDataDTO meta = new ProductProcessDTO.MetaDataDTO();
        meta.setSn("TEST_SN_" + System.currentTimeMillis());
        meta.setLineCode("LINE001");
        meta.setOperatorId("OP001");
        meta.setProcessCode("PROC001");
        dto.setMeta(meta);
        
        // 设置参数
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("voltage", 12.5);
        parameters.put("temperature", 25.0);
        dto.setParameters(parameters);
        
        return dto;
    }
}
