# MongoDB配置迁移到Nacos配置中心

## 迁移概述

MongoDB模块的配置已经成功迁移到Nacos配置中心，与项目中的MySQL和Redis配置保持一致的管理方式。

## 迁移内容

### 1. 修复的问题
- ✅ 修复了`SocketSettings.Builder.keepAlive(boolean)`弃用方法警告
- ✅ 将MongoDB配置从本地配置文件迁移到Nacos配置中心

### 2. 配置文件变更

#### 修改的文件：
- `bootstrap.properties` - 移除MongoDB配置，添加Nacos配置
- `MongodbConfig.java` - 修改为从配置中心读取参数
- 新增 `registry.conf` - Nacos注册配置
- 新增 `registry-dev.conf` - Nacos开发环境配置

#### 新增的配置示例：
- `nacos-config-example/mes-mongodb.properties` - Nacos配置模板
- `nacos-config-example/README.md` - 配置说明文档

### 3. 配置迁移对比

#### 迁移前（本地配置）：
```properties
# 在 bootstrap.properties 中
spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.database=mes_mongodb
# ... 其他配置
```

#### 迁移后（Nacos配置）：
```properties
# 在 Nacos 配置中心的 mes-mongodb.properties 中
spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.database=mes_mongodb
# ... 其他配置
```

## 使用说明

### 1. 部署前准备
1. 确保Nacos服务正常运行
2. 登录Nacos控制台：http://localhost:8848/nacos
3. 切换到命名空间：`9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`

   **重要说明**：虽然registry.conf中配置的是`4d307a39-1034-4861-83fd-c6921c6d9c02`，
   但Spring Boot应用实际使用的是`NacosConstant.java`中定义的命名空间`9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05`

### 2. 创建MongoDB配置
在Nacos配置管理中创建：
- **Data ID**: `mes-mongodb.properties`
- **Group**: `DEFAULT_GROUP`
- **配置内容**: 复制 `nacos-config-example/mes-mongodb.properties` 的内容

### 3. 启动服务
```bash
# 编译项目
mvn clean install

# 启动MongoDB服务
java -jar mes-module-mongodb-service/target/mes-mongodb.jar
```

## 配置优势

1. **统一管理**: 与其他数据源配置保持一致
2. **动态更新**: 配置修改后无需重启服务
3. **环境隔离**: 支持多环境配置管理
4. **安全性**: 敏感信息集中管理
5. **版本控制**: 支持配置版本管理和回滚

## 注意事项

1. **首次部署**: 必须先在Nacos中创建MongoDB配置
2. **配置格式**: 确保配置文件格式为Properties
3. **命名空间**: 必须使用正确的命名空间ID
4. **网络连接**: 确保服务能够访问Nacos和MongoDB

## 故障排查

### 常见问题：
1. **服务启动失败**: 检查Nacos配置是否正确创建
2. **MongoDB连接失败**: 检查MongoDB服务状态和网络连接
3. **配置不生效**: 检查命名空间和Group是否正确

### 日志查看：
```bash
# 查看MongoDB连接日志
tail -f logs/mes-mongodb.log | grep -i mongodb

# 查看Nacos配置加载日志
tail -f logs/mes-mongodb.log | grep -i nacos
```

## 技术细节

### 代码变更：
1. 移除了硬编码的MongoDB配置
2. 使用`@Value`注解从配置中心读取参数
3. 修复了弃用的`keepAlive(boolean)`方法
4. 添加了完整的连接池配置支持

### 配置参数：
- 支持所有MongoDB连接参数的动态配置
- 支持连接池参数的精细化控制
- 支持SSL和认证相关配置

这次迁移确保了MongoDB模块与整个MES系统的配置管理保持一致，提高了系统的可维护性和可扩展性。
