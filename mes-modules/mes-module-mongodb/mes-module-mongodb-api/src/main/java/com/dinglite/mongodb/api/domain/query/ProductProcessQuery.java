package com.dinglite.mongodb.api.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品过站记录查询对象
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProductProcessQuery对象", description = "产品过站记录查询条件")
public class ProductProcessQuery extends BaseQuery {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品序列号")
    private String sn;

    @ApiModelProperty(value = "产品序列号列表")
    private List<String> snList;

    @ApiModelProperty(value = "产线编码")
    private String lineCode;

    @ApiModelProperty(value = "产线名称")
    private String lineName;

    @ApiModelProperty(value = "工序编码")
    private String processCode;

    @ApiModelProperty(value = "工序名称")
    private String processName;

    @ApiModelProperty(value = "工站名称")
    private String stationName;

    @ApiModelProperty(value = "工站IP地址")
    private String stationIP;

    @ApiModelProperty(value = "操作员账号")
    private String operatorId;

    @ApiModelProperty(value = "操作员姓名")
    private String operatorName;

    @ApiModelProperty(value = "测试结果（PASS/FAIL）")
    private String result;

    @ApiModelProperty(value = "测试结果列表")
    private List<String> resultList;

    @ApiModelProperty(value = "是否首道工序")
    private Boolean isFirstProcess;

    @ApiModelProperty(value = "是否补扫记录")
    private Boolean isRepairScan;

    @ApiModelProperty(value = "过站开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processTimeStart;

    @ApiModelProperty(value = "过站结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processTimeEnd;

    @ApiModelProperty(value = "扫描开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime scanTimeStart;

    @ApiModelProperty(value = "扫描结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime scanTimeEnd;

    @ApiModelProperty(value = "提交开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTimeStart;

    @ApiModelProperty(value = "提交结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTimeEnd;

    @ApiModelProperty(value = "治具名称")
    private String fixtureName;

    @ApiModelProperty(value = "治具编码")
    private String fixtureCode;

    @ApiModelProperty(value = "治具版本")
    private String fixtureVersion;

    @ApiModelProperty(value = "扫描编码")
    private String scanCode;

    @ApiModelProperty(value = "不良名称")
    private String failName;

    @ApiModelProperty(value = "不良编码")
    private String failCode;

    @ApiModelProperty(value = "重投工序编码")
    private String reworkProcessCode;

    @ApiModelProperty(value = "重投工序名称")
    private String reworkProcessName;
}
