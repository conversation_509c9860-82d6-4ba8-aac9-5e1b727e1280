package com.dinglite.mongodb.api.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 包装信息数据模型
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PackageInfo对象", description = "包装信息")
@Document(collection = "package_info")
public class PackageInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "MongoDB自动生成的唯一标识符")
    @Id
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "目标集合名称（动态指定存储位置）")
    @Field("collection")
    @JsonProperty("collection")
    private String collection;

    @ApiModelProperty(value = "包装层级类型")
    @Field("packageType")
    @JsonProperty("packageType")
    private String packageType;

    @ApiModelProperty(value = "包装编码（唯一标识）")
    @Field("packageCode")
    @JsonProperty("packageCode")
    private String packageCode;

    @ApiModelProperty(value = "包装名称")
    @Field("packageName")
    @JsonProperty("packageName")
    private String packageName;

    @ApiModelProperty(value = "父级包装编码")
    @Field("parentPackageCode")
    @JsonProperty("parentPackageCode")
    private String parentPackageCode;

    @ApiModelProperty(value = "包装容量（最大可容纳数量）")
    @Field("capacity")
    @JsonProperty("capacity")
    private Integer capacity;

    @ApiModelProperty(value = "当前已包装数量")
    @Field("currentCount")
    @JsonProperty("currentCount")
    private Integer currentCount;

    @ApiModelProperty(value = "包装状态")
    @Field("status")
    @JsonProperty("status")
    private String status;

    @ApiModelProperty(value = "产品型号编码")
    @Field("modelCode")
    @JsonProperty("modelCode")
    private String modelCode;

    @ApiModelProperty(value = "产品型号名称")
    @Field("modelName")
    @JsonProperty("modelName")
    private String modelName;

    @ApiModelProperty(value = "生产工单号")
    @Field("productionOrder")
    @JsonProperty("productionOrder")
    private String productionOrder;

    @ApiModelProperty(value = "产线名称")
    @Field("lineName")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    @Field("lineCode")
    @JsonProperty("lineCode")
    private String lineCode;

    @ApiModelProperty(value = "包装工站名称")
    @Field("stationName")
    @JsonProperty("stationName")
    private String stationName;

    @ApiModelProperty(value = "包装工站IP")
    @Field("stationIP")
    @JsonProperty("stationIP")
    private String stationIP;

    @ApiModelProperty(value = "操作员账号")
    @Field("operatorId")
    @JsonProperty("operatorId")
    private String operatorId;

    @ApiModelProperty(value = "操作员姓名")
    @Field("operatorName")
    @JsonProperty("operatorName")
    private String operatorName;

    @ApiModelProperty(value = "包装开始时间")
    @Field("startTime")
    @JsonProperty("startTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "包装完成时间")
    @Field("endTime")
    @JsonProperty("endTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "包装内产品序列号列表")
    @Field("productSnList")
    @JsonProperty("productSnList")
    private List<String> productSnList = new ArrayList<>();

    @ApiModelProperty(value = "子包装编码列表")
    @Field("childPackageCodes")
    @JsonProperty("childPackageCodes")
    private List<String> childPackageCodes = new ArrayList<>();

    @ApiModelProperty(value = "包装规格描述")
    @Field("specification")
    @JsonProperty("specification")
    private String specification;

    @ApiModelProperty(value = "包装重量（克）")
    @Field("weight")
    @JsonProperty("weight")
    private Double weight;

    @ApiModelProperty(value = "包装尺寸（长x宽x高，单位：毫米）")
    @Field("dimensions")
    @JsonProperty("dimensions")
    private String dimensions;

    @ApiModelProperty(value = "备注信息")
    @Field("remark")
    @JsonProperty("remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @Field("createTime")
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @Field("updateTime")
    @JsonProperty("updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 包装类型枚举
     */
    public enum PackageType {
        小包("小包", "小包装"),
        内箱("内箱", "内箱包装"),
        外箱("外箱", "外箱包装"),
        栈板("栈板", "栈板包装");

        private final String code;
        private final String description;

        PackageType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 包装状态枚举
     */
    public enum PackageStatus {
        包装中("包装中", "正在进行包装"),
        已完成("已完成", "包装已完成"),
        已封箱("已封箱", "已封箱完成"),
        已入库("已入库", "已入库存储"),
        已出库("已出库", "已出库发货");

        private final String code;
        private final String description;

        PackageStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
