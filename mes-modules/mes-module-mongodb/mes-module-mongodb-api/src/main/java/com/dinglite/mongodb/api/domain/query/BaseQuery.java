package com.dinglite.mongodb.api.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询对象基类
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@ApiModel(value = "BaseQuery对象", description = "查询对象基类")
public class BaseQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer current = 1;

    @ApiModelProperty(value = "每页记录数", example = "10")
    private Integer size = 10;

    @ApiModelProperty(value = "排序字段")
    private String orderField;

    @ApiModelProperty(value = "排序方式（asc/desc）")
    private String orderType = "desc";
}
