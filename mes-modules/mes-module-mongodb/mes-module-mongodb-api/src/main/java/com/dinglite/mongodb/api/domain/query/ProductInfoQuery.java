package com.dinglite.mongodb.api.domain.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品基础信息查询对象
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProductInfoQuery对象", description = "产品基础信息查询条件")
public class ProductInfoQuery extends BaseQuery {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品序列号")
    private String sn;

    @ApiModelProperty(value = "产品序列号列表")
    private List<String> snList;

    @ApiModelProperty(value = "产品型号名称")
    private String modelName;

    @ApiModelProperty(value = "产品型号编码")
    private String modelCode;

    @ApiModelProperty(value = "客户物料号")
    private String customerPN;

    @ApiModelProperty(value = "客户订单号")
    private String customerOrder;

    @ApiModelProperty(value = "生产工单号")
    private String productionOrder;

    @ApiModelProperty(value = "生产车间名称")
    private String workshopName;

    @ApiModelProperty(value = "产线名称")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    private String lineCode;

    @ApiModelProperty(value = "产品状态")
    private String status;

    @ApiModelProperty(value = "产品状态列表")
    private List<String> statusList;

    @ApiModelProperty(value = "是否直通品标识")
    private Boolean isDirectProduct;

    @ApiModelProperty(value = "投产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTimeStart;

    @ApiModelProperty(value = "投产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTimeEnd;

    @ApiModelProperty(value = "最后工序名称")
    private String lastProcess;

    @ApiModelProperty(value = "最后工站名称")
    private String lastStation;

    @ApiModelProperty(value = "包装状态")
    private String packageStatus;

    @ApiModelProperty(value = "包装状态列表")
    private List<String> packageStatusList;

    @ApiModelProperty(value = "小包装编码")
    private String packageCode;

    @ApiModelProperty(value = "内箱编码")
    private String innerBoxCode;

    @ApiModelProperty(value = "外箱编码")
    private String outerBoxCode;

    @ApiModelProperty(value = "栈板编码")
    private String palletCode;

    @ApiModelProperty(value = "入库开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inboundTimeStart;

    @ApiModelProperty(value = "入库结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inboundTimeEnd;

    @ApiModelProperty(value = "出库开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime outboundTimeStart;

    @ApiModelProperty(value = "出库结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime outboundTimeEnd;
}
