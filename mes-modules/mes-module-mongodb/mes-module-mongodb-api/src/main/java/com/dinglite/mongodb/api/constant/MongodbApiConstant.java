package com.dinglite.mongodb.api.constant;

/**
 * MongoDB模块API常量
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
public class MongodbApiConstant {

    /**
     * Feign服务名称
     */
    public static final String FEIGN_NAME = "mes-mongodb";

    /**
     * Feign包扫描路径
     */
    public static final String FEIGN_PACKAGE = "com.dinglite.mongodb.api.service";

    /**
     * API基础路径
     */
    public static final String API_BASE_PATH = "/mongodb";

    /**
     * 产品过站记录API路径
     */
    public static final String PRODUCT_PROCESS_API_PATH = API_BASE_PATH + "/product-process";

    /**
     * 产品信息API路径
     */
    public static final String PRODUCT_INFO_API_PATH = API_BASE_PATH + "/product-info";

    /**
     * 产品异常API路径
     */
    public static final String PRODUCT_EXCEPTION_API_PATH = API_BASE_PATH + "/product-exception";

    /**
     * 产品物料API路径
     */
    public static final String PRODUCT_MATERIAL_API_PATH = API_BASE_PATH + "/product-material";

    /**
     * 包装信息API路径
     */
    public static final String PACKAGE_INFO_API_PATH = API_BASE_PATH + "/package-info";

    /**
     * MongoDB集合名称常量
     */
    public static class Collections {
        public static final String PRODUCT_PROCESS = "product_process";
        public static final String PRODUCT_INFO = "product_info";
        public static final String PRODUCT_EXCEPTION = "product_exception";
        public static final String PRODUCT_MATERIAL = "product_material";
        public static final String PACKAGE_INFO = "package_info";
    }
}
