<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.dinglite.mes</groupId>
    <artifactId>mes-system</artifactId>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>mes-common</module>
        <module>mes-basic</module>
        <module>mes-modules</module>
        <module>mes-gateway</module>
        <module>mes-control-business</module>
        <module>mes-rocketmq</module>
        <module>mes-monitor</module>
        <module>mes-server</module>
    </modules>
    <packaging>pom</packaging>
    <name>A-mes系统（总模块）</name>


    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.6.RELEASE</version>
    </parent>

    <properties>
        <!-- 自动推送镜像相关 -->
        <registry.baseurl />
        <registry.username />
        <registry.password />
        <google-jib.version>3.0.0</google-jib.version>

        <lombok.version>1.18.8</lombok.version>
        <druid.version>1.1.22</druid.version>
        <common.version>3.10</common.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>

        <spring.boot.version>2.1.6.RELEASE</spring.boot.version>
        <druid-spring-boot-starter.version>1.1.21</druid-spring-boot-starter.version>
        <mybatis-spring-boot-starter.version>2.0.1</mybatis-spring-boot-starter.version>
        <mybatis-plus.version>3.3.0</mybatis-plus.version>
        <mybatis-plus-join.version>1.1.8</mybatis-plus-join.version>
        <hutool.version>4.5.6</hutool.version>
        <commons-io.version>2.6</commons-io.version>
        <fastjson.version>1.2.9</fastjson.version>
        <guava.version>19.0</guava.version>
        <swagger.version>2.9.2</swagger.version>
        <swagger.models.version>1.5.21</swagger.models.version>
        <objenesis.version>2.6</objenesis.version>
        <alibaba-seata.version>2.2.0.RELEASE</alibaba-seata.version>
        <knife4j-micro-spring-boot-starter.version>2.0.4</knife4j-micro-spring-boot-starter.version>
        <dynamic-datasource.version>3.2.0</dynamic-datasource.version>
        <seata.version>2.2.1.RELEASE</seata.version>

        <!-- Maven Plugin Versions -->
        <maven-compiler-plugin.version>3.7.0</maven-compiler-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-surefire-plugin.version>2.21.0</maven-surefire-plugin.version>
        <maven-jar-plugin.version>3.0.2</maven-jar-plugin.version>
        <maven-install-plugin.version>2.5.2</maven-install-plugin.version>
        <maven-resources-plugin.version>3.0.2</maven-resources-plugin.version>
        <maven-clean-plugin.version>3.0.0</maven-clean-plugin.version>

        <redission.version>3.13.2</redission.version>
        <data-redis.version>2.1.9.RELEASE</data-redis.version>
        <kaptcha.version>0.0.9</kaptcha.version>
        <spring-boot-admin.version>2.1.6</spring-boot-admin.version>
        <mongodb.version>3.12.11</mongodb.version>
        <spring-data-mongodb.version>2.1.9.RELEASE</spring-data-mongodb.version>
    </properties>


    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Greenwich.SR2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud Alibaba dependencies -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>0.2.2.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- swagger2 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.models.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${common.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redission.version}</version>

                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.data</groupId>
                        <artifactId>spring-data-redis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- MongoDB dependencies -->
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-mongodb</artifactId>
                <version>${spring-data-mongodb.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver</artifactId>
                <version>${mongodb.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-core</artifactId>
                <version>${mongodb.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>bson</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>compile</scope>
        </dependency>


        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
    </dependencies>

    <build>
<!--        xxxManagement标签用于子模块继承父模块的配置，如继承spring-boot-starter-parent的配置。-->
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${maven-clean-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven-install-plugin.version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>

            </plugins>
        </pluginManagement>
    </build>

</project>